<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dedicated Streaming Servers - X-ZoneServers | High-Performance Bare Metal Hosting</title>
    <meta name="description" content="Powerful dedicated streaming servers with dual Xeon processors, 10Gbps-100Gbps guaranteed bandwidth, global locations. Starting from €229/month.">
    <meta name="keywords" content="dedicated servers, streaming servers, bare metal, dual xeon, 10gbps dedicated, offshore dedicated servers, media streaming">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <link rel="stylesheet" href="style.css">
    <script src="header-loader.js"></script>
</head>
<body class="antialiased">
    <!-- Header Placeholder -->
    <div id="header-placeholder"></div>

    <main>
        <!-- Hero Section with Video Background Concept -->
        <section class="relative pt-32 pb-24 overflow-hidden bg-slate-950">
            <div class="absolute inset-0 bg-gradient-to-r from-slate-950 via-slate-950/95 to-slate-950/90"></div>
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23334155" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
            
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="max-w-4xl">
                    <div class="flex items-center mb-6">
                        <div class="bg-gradient-to-r from-red-500 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-bold mr-4">
                            ENTERPRISE GRADE
                        </div>
                        <div class="text-gray-400 text-sm">🌍 13 Global Locations Available</div>
                    </div>
                    
                    <h1 class="text-5xl md:text-7xl font-extrabold text-white leading-tight mb-8">
                        Dedicated<br>
                        <span class="bg-gradient-to-r from-sky-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
                            Streaming Servers
                        </span>
                    </h1>
                    
                    <p class="text-xl text-gray-300 mb-8 max-w-2xl">
                        Unleash maximum performance with bare metal dedicated servers. Dual Xeon processors, guaranteed bandwidth up to 100Gbps, and enterprise-grade infrastructure across premium datacenters worldwide.
                    </p>
                    
                    <div class="flex flex-wrap gap-6 mb-12">
                        <div class="flex items-center text-gray-300">
                            <div class="w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                            <span class="text-sm">1 Hour Deployment</span>
                        </div>
                        <div class="flex items-center text-gray-300">
                            <div class="w-3 h-3 bg-blue-400 rounded-full mr-3"></div>
                            <span class="text-sm">Guaranteed Bandwidth</span>
                        </div>
                        <div class="flex items-center text-gray-300">
                            <div class="w-3 h-3 bg-purple-400 rounded-full mr-3"></div>
                            <span class="text-sm">Premium Hardware</span>
                        </div>
                    </div>
                    
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="#server-configurator" class="btn-primary px-10 py-4 rounded-xl font-bold text-lg inline-flex items-center justify-center">
                            <i data-lucide="settings" class="w-5 h-5 mr-2"></i>
                            Configure Your Server
                        </a>
                        <a href="#server-comparison" class="btn-secondary px-10 py-4 rounded-xl font-bold text-lg inline-flex items-center justify-center">
                            <i data-lucide="bar-chart-3" class="w-5 h-5 mr-2"></i>
                            Compare Servers
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Performance Metrics Bar -->
        <section class="py-12 bg-gradient-to-r from-slate-900 to-slate-800 border-y border-slate-700/50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">25Gbps</div>
                        <div class="text-sky-400 font-medium text-sm">Max Bandwidth</div>
                        <div class="text-gray-400 text-xs">Guaranteed speed</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">512</div>
                        <div class="text-sky-400 font-medium text-sm">CPU Threads</div>
                        <div class="text-gray-400 text-xs">AMD EPYC power</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">1TB</div>
                        <div class="text-sky-400 font-medium text-sm">Max RAM</div>
                        <div class="text-gray-400 text-xs">DDR5 memory</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">24</div>
                        <div class="text-sky-400 font-medium text-sm">NVMe Slots</div>
                        <div class="text-gray-400 text-xs">Maximum capacity</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Server Configurator Section -->
        <section id="server-configurator" class="py-24">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">Choose Your Configuration</h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        Select from our pre-configured dedicated streaming servers or build a custom solution for your specific needs.
                    </p>
                </div>

                <!-- Intel Xeon Servers -->
                <div class="mb-12">
                    <h3 class="text-2xl font-bold text-white mb-2 text-center">Intel Xeon Dedicated Servers</h3>
                    <p class="text-gray-400 text-center mb-8">High-performance Intel processors optimized for streaming and media delivery</p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Xeon E5-2630v3 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-cyan-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-blue-500/50 hover:bg-slate-800/50">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="text-xl font-bold text-white">Dual Xeon E5-2630v3</h4>
                                        <p class="text-blue-400 font-medium text-sm">Entry Level</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-white">€209</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>
                                
                                <div class="space-y-3 mb-6 text-sm">
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">16c / 32t</span>
                                        <span class="text-gray-300">128GB RAM</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">240GB SSD</span>
                                        <span class="text-gray-300">1/5/10Gbps</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">🇷🇴 Romania</span>
                                        <span class="text-green-400">1 Hour</span>
                                    </div>
                                </div>
                                
                                <button class="w-full btn-primary py-3 rounded-lg font-semibold text-sm">
                                    Configure
                                </button>
                            </div>
                        </div>

                        <!-- Xeon E5-2690v4 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-sky-500/10 to-blue-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-sky-500/50 hover:bg-slate-800/50">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="text-xl font-bold text-white">Dual Xeon E5-2690v4</h4>
                                        <p class="text-sky-400 font-medium text-sm">Popular Choice</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-white">€229</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>
                                
                                <div class="space-y-3 mb-6 text-sm">
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">28c / 56t</span>
                                        <span class="text-gray-300">128GB RAM</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">240GB SSD</span>
                                        <span class="text-gray-300">1/5/10Gbps</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">🇩🇪🇷🇴🇳🇱🇮🇹</span>
                                        <span class="text-green-400">1 Hour</span>
                                    </div>
                                </div>
                                
                                <button class="w-full btn-primary py-3 rounded-lg font-semibold text-sm">
                                    Configure
                                </button>
                            </div>
                        </div>

                        <!-- Xeon E5-2699v4 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-purple-500/50 hover:bg-slate-800/50">
                                <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                    <div class="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-1 rounded-full text-xs font-bold">
                                        POPULAR
                                    </div>
                                </div>
                                
                                <div class="flex items-center justify-between mb-4 mt-2">
                                    <div>
                                        <h4 class="text-xl font-bold text-white">Dual Xeon E5-2699v4</h4>
                                        <p class="text-purple-400 font-medium text-sm">High Performance</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-white">€299</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>
                                
                                <div class="space-y-3 mb-6 text-sm">
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">40c / 80t</span>
                                        <span class="text-gray-300">256GB RAM</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">240GB SSD</span>
                                        <span class="text-gray-300">1/5/10Gbps</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">🇩🇪🇷🇴🇳🇱🇮🇹</span>
                                        <span class="text-green-400">1 Hour</span>
                                    </div>
                                </div>
                                
                                <button class="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 rounded-lg font-semibold text-sm">
                                    Configure
                                </button>
                            </div>
                        </div>

                        <!-- Xeon Gold 6230 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-yellow-500/10 to-orange-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-yellow-500/50 hover:bg-slate-800/50">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="text-xl font-bold text-white">Dual Xeon Gold 6230</h4>
                                        <p class="text-yellow-400 font-medium text-sm">Premium Performance</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-white">€309</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>
                                
                                <div class="space-y-3 mb-6 text-sm">
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">40c / 80t</span>
                                        <span class="text-gray-300">256GB RAM</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">240GB SSD</span>
                                        <span class="text-gray-300">1/5/10Gbps</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">🇩🇪🇷🇴🇳🇱🇮🇹</span>
                                        <span class="text-green-400">1 Hour</span>
                                    </div>
                                </div>
                                
                                <button class="w-full bg-gradient-to-r from-yellow-600 to-orange-600 text-white py-3 rounded-lg font-semibold text-sm">
                                    Configure
                                </button>
                            </div>
                        </div>

                        <!-- Xeon Gold 6148 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-red-500/10 to-orange-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-red-500/50 hover:bg-slate-800/50">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="text-xl font-bold text-white">Dual Xeon Gold 6148</h4>
                                        <p class="text-red-400 font-medium text-sm">Ultimate Performance</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-white">€1799</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>
                                
                                <div class="space-y-3 mb-6 text-sm">
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">40c / 80t</span>
                                        <span class="text-gray-300">256GB RAM</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">240GB SSD</span>
                                        <span class="text-red-400 font-semibold">25Gbps</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">🇩🇪🇷🇴🇳🇱🇮🇹</span>
                                        <span class="text-green-400">1 Hour</span>
                                    </div>
                                </div>
                                
                                <button class="w-full bg-gradient-to-r from-red-600 to-orange-600 text-white py-3 rounded-lg font-semibold text-sm">
                                    Configure
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AMD EPYC Servers -->
                <div class="mb-16">
                    <h3 class="text-2xl font-bold text-white mb-2 text-center">AMD EPYC AI-Optimized Servers</h3>
                    <p class="text-gray-400 text-center mb-8">Next-generation AMD processors with DDR5 memory and AI-optimized routing</p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <!-- Single AMD EPYC 9254 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-emerald-500/10 to-teal-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-emerald-500/50 hover:bg-slate-800/50">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="text-lg font-bold text-white">Single EPYC 9254</h4>
                                        <p class="text-emerald-400 font-medium text-xs">AI Entry Level</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xl font-bold text-white">€449</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>
                                
                                <div class="space-y-2 mb-6 text-xs">
                                    <div class="text-gray-300">24c / 48t @2.9GHz</div>
                                    <div class="text-gray-300">128GB DDR5 @4800MHz</div>
                                    <div class="text-gray-300">10 x NVMe Slots</div>
                                    <div class="text-gray-300">1Gbps Guaranteed</div>
                                    <div class="text-emerald-400 font-medium">AI Optimized Routing</div>
                                </div>
                                
                                <button class="w-full bg-gradient-to-r from-emerald-600 to-teal-600 text-white py-3 rounded-lg font-semibold text-sm">
                                    Configure
                                </button>
                            </div>
                        </div>

                        <!-- Dual AMD EPYC 9254 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-cyan-500/10 to-blue-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-cyan-500/50 hover:bg-slate-800/50">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="text-lg font-bold text-white">Dual EPYC 9254</h4>
                                        <p class="text-cyan-400 font-medium text-xs">AI Performance</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xl font-bold text-white">€679</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>
                                
                                <div class="space-y-2 mb-6 text-xs">
                                    <div class="text-gray-300">48c / 96t @2.9GHz</div>
                                    <div class="text-gray-300">256GB DDR5 @4800MHz</div>
                                    <div class="text-gray-300">10 x NVMe Slots</div>
                                    <div class="text-gray-300">1Gbps Guaranteed</div>
                                    <div class="text-cyan-400 font-medium">AI Optimized Routing</div>
                                </div>
                                
                                <button class="w-full bg-gradient-to-r from-cyan-600 to-blue-600 text-white py-3 rounded-lg font-semibold text-sm">
                                    Configure
                                </button>
                            </div>
                        </div>

                        <!-- Dual AMD EPYC 9554 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-violet-500/10 to-purple-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-violet-500/50 hover:bg-slate-800/50">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="text-lg font-bold text-white">Dual EPYC 9554</h4>
                                        <p class="text-violet-400 font-medium text-xs">AI High-End</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xl font-bold text-white">€959</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>
                                
                                <div class="space-y-2 mb-6 text-xs">
                                    <div class="text-gray-300">128c / 256t @3.1GHz</div>
                                    <div class="text-gray-300">512GB DDR5 @4800MHz</div>
                                    <div class="text-gray-300">24 x NVMe Slots</div>
                                    <div class="text-gray-300">1Gbps Guaranteed</div>
                                    <div class="text-violet-400 font-medium">AI Optimized Routing</div>
                                </div>
                                
                                <button class="w-full bg-gradient-to-r from-violet-600 to-purple-600 text-white py-3 rounded-lg font-semibold text-sm">
                                    Configure
                                </button>
                            </div>
                        </div>

                        <!-- Dual AMD EPYC 9754 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-pink-500/10 to-rose-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-pink-500/50 hover:bg-slate-800/50">
                                <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                    <div class="bg-gradient-to-r from-pink-600 to-rose-600 text-white px-3 py-1 rounded-full text-xs font-bold">
                                        ULTIMATE
                                    </div>
                                </div>
                                
                                <div class="flex items-center justify-between mb-4 mt-2">
                                    <div>
                                        <h4 class="text-lg font-bold text-white">Dual EPYC 9754</h4>
                                        <p class="text-pink-400 font-medium text-xs">AI Ultimate</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xl font-bold text-white">€1519</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>
                                
                                <div class="space-y-2 mb-6 text-xs">
                                    <div class="text-gray-300">256c / 512t @2.3GHz</div>
                                    <div class="text-gray-300">1024GB DDR5 @4800MHz</div>
                                    <div class="text-gray-300">24 x NVMe Slots</div>
                                    <div class="text-gray-300">1Gbps Guaranteed</div>
                                    <div class="text-pink-400 font-medium">AI Optimized Routing</div>
                                </div>
                                
                                <button class="w-full bg-gradient-to-r from-pink-600 to-rose-600 text-white py-3 rounded-lg font-semibold text-sm">
                                    Configure
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Custom Configuration CTA -->
                <div class="text-center bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-2xl p-12 border border-slate-600/50">
                    <h3 class="text-2xl font-bold text-white mb-4">Need a Custom Configuration?</h3>
                    <p class="text-gray-300 mb-6 max-w-2xl mx-auto">
                        Our enterprise team can design a custom dedicated server solution tailored to your specific streaming and hosting requirements.
                    </p>
                    <button class="btn-secondary px-8 py-3 rounded-lg font-semibold inline-flex items-center">
                        <i data-lucide="phone" class="w-4 h-4 mr-2"></i>
                        Contact Enterprise Team
                    </button>
                </div>
            </div>
        </section>

        <!-- Global Network Section -->
        <section class="py-24 bg-slate-950/30">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">Global Network Infrastructure</h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        Deploy your dedicated servers across 13 premium locations worldwide with tier-1 network connectivity and low-latency routing.
                    </p>
                </div>

                <!-- Interactive Network Map Concept -->
                <div class="relative max-w-6xl mx-auto mb-16">
                    <div class="bg-gradient-to-br from-slate-800/30 to-slate-900/50 rounded-3xl p-8 border border-slate-700/50">
                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                            <!-- Europe Locations -->
                            <div class="text-center group cursor-pointer">
                                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-sky-600 rounded-2xl flex items-center justify-center mx-auto mb-3 transition-transform group-hover:scale-110">
                                    <span class="text-2xl">🇷🇴</span>
                                </div>
                                <h4 class="text-white font-semibold">Bucharest</h4>
                                <p class="text-gray-400 text-sm">Voxility IRD</p>
                                <div class="mt-2 text-xs text-green-400">●  Available</div>
                            </div>
                            
                            <div class="text-center group cursor-pointer">
                                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-3 transition-transform group-hover:scale-110">
                                    <span class="text-2xl">🇮🇹</span>
                                </div>
                                <h4 class="text-white font-semibold">Milan</h4>
                                <p class="text-gray-400 text-sm">Irideos, Seeweb</p>
                                <div class="mt-2 text-xs text-green-400">●  Available</div>
                            </div>
                            
                            <div class="text-center group cursor-pointer">
                                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-3 transition-transform group-hover:scale-110">
                                    <span class="text-2xl">🇩🇪</span>
                                </div>
                                <h4 class="text-white font-semibold">Frankfurt</h4>
                                <p class="text-gray-400 text-sm">Equinix, Interxion</p>
                                <div class="mt-2 text-xs text-green-400">●  Available</div>
                            </div>
                            
                            <div class="text-center group cursor-pointer">
                                <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-3 transition-transform group-hover:scale-110">
                                    <span class="text-2xl">🇳🇱</span>
                                </div>
                                <h4 class="text-white font-semibold">Amsterdam</h4>
                                <p class="text-gray-400 text-sm">Equinix</p>
                                <div class="mt-2 text-xs text-green-400">●  Available</div>
                            </div>
                            
                            <div class="text-center group cursor-pointer">
                                <div class="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-3 transition-transform group-hover:scale-110">
                                    <span class="text-2xl">🇫🇷</span>
                                </div>
                                <h4 class="text-white font-semibold">Paris</h4>
                                <p class="text-gray-400 text-sm">Equinix, Interxion</p>
                                <div class="mt-2 text-xs text-green-400">●  Available</div>
                            </div>
                            
                            <div class="text-center group cursor-pointer">
                                <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-3 transition-transform group-hover:scale-110">
                                    <span class="text-2xl">🇬🇧</span>
                                </div>
                                <h4 class="text-white font-semibold">London</h4>
                                <p class="text-gray-400 text-sm">Telehouse, Equinix</p>
                                <div class="mt-2 text-xs text-green-400">●  Available</div>
                            </div>
                            
                            <div class="text-center group cursor-pointer">
                                <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-3 transition-transform group-hover:scale-110">
                                    <span class="text-2xl">🇪🇸</span>
                                </div>
                                <h4 class="text-white font-semibold">Madrid</h4>
                                <p class="text-gray-400 text-sm">Equinix, Interxion</p>
                                <div class="mt-2 text-xs text-green-400">●  Available</div>
                            </div>
                            
                            <!-- US Locations -->
                            <div class="text-center group cursor-pointer">
                                <div class="w-16 h-16 bg-gradient-to-br from-red-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-3 transition-transform group-hover:scale-110">
                                    <span class="text-2xl">🇺🇸</span>
                                </div>
                                <h4 class="text-white font-semibold">Ashburn</h4>
                                <p class="text-gray-400 text-sm">Equinix</p>
                                <div class="mt-2 text-xs text-green-400">●  Available</div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-12">
                            <p class="text-gray-400 text-sm mb-4">Select your preferred location during server configuration</p>
                            <div class="inline-flex items-center text-green-400 text-sm">
                                <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                                All locations ready for instant deployment
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Server Comparison Table -->
        <section id="server-comparison" class="py-24">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">Server Comparison</h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        Compare specifications and choose the perfect dedicated server configuration for your streaming needs.
                    </p>
                </div>

                <!-- Intel Xeon Comparison -->
                <div class="bg-slate-900/30 rounded-3xl p-8 border border-slate-700/50 overflow-x-auto mb-8">
                    <h4 class="text-xl font-bold text-white mb-6 text-center">Intel Xeon Server Comparison</h4>
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="border-b-2 border-slate-700">
                                <th class="text-left py-4 px-3 text-gray-300 font-semibold">Specifications</th>
                                <th class="text-center py-4 px-3 text-blue-400 font-bold text-xs">E5-2630v3<br><span class="text-gray-400 font-normal">€209/mo</span></th>
                                <th class="text-center py-4 px-3 text-sky-400 font-bold text-xs">E5-2690v4<br><span class="text-gray-400 font-normal">€229/mo</span></th>
                                <th class="text-center py-4 px-3 text-purple-400 font-bold text-xs relative">
                                    E5-2699v4<br><span class="text-gray-400 font-normal">€299/mo</span>
                                    <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
                                        <span class="bg-purple-600 text-white px-2 py-1 rounded text-xs font-bold">POPULAR</span>
                                    </div>
                                </th>
                                <th class="text-center py-4 px-3 text-yellow-400 font-bold text-xs">Gold 6230<br><span class="text-gray-400 font-normal">€309/mo</span></th>
                                <th class="text-center py-4 px-3 text-red-400 font-bold text-xs">Gold 6148<br><span class="text-gray-400 font-normal">€1799/mo</span></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-3 text-gray-300 font-medium">Cores / Threads</td>
                                <td class="py-3 px-3 text-center text-blue-400 font-bold">16c / 32t</td>
                                <td class="py-3 px-3 text-center text-sky-400 font-bold">28c / 56t</td>
                                <td class="py-3 px-3 text-center text-purple-400 font-bold">40c / 80t</td>
                                <td class="py-3 px-3 text-center text-yellow-400 font-bold">40c / 80t</td>
                                <td class="py-3 px-3 text-center text-red-400 font-bold">40c / 80t</td>
                            </tr>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-3 text-gray-300 font-medium">RAM Memory</td>
                                <td class="py-3 px-3 text-center text-white">128 GB</td>
                                <td class="py-3 px-3 text-center text-white">128 GB</td>
                                <td class="py-3 px-3 text-center text-white">256 GB</td>
                                <td class="py-3 px-3 text-center text-white">256 GB</td>
                                <td class="py-3 px-3 text-center text-white">256 GB</td>
                            </tr>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-3 text-gray-300 font-medium">Storage</td>
                                <td class="py-3 px-3 text-center text-white">240GB SSD</td>
                                <td class="py-3 px-3 text-center text-white">240GB SSD</td>
                                <td class="py-3 px-3 text-center text-white">240GB SSD</td>
                                <td class="py-3 px-3 text-center text-white">240GB SSD</td>
                                <td class="py-3 px-3 text-center text-white">240GB SSD</td>
                            </tr>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-3 text-gray-300 font-medium">Bandwidth</td>
                                <td class="py-3 px-3 text-center text-white">1/5/10 Gbps</td>
                                <td class="py-3 px-3 text-center text-white">1/5/10 Gbps</td>
                                <td class="py-3 px-3 text-center text-white">1/5/10 Gbps</td>
                                <td class="py-3 px-3 text-center text-white">1/5/10 Gbps</td>
                                <td class="py-3 px-3 text-center text-red-400 font-bold">25 Gbps</td>
                            </tr>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-3 text-gray-300 font-medium">Locations</td>
                                <td class="py-3 px-3 text-center text-white">🇷🇴</td>
                                <td class="py-3 px-3 text-center text-white">🇩🇪🇷🇴🇳🇱🇮🇹</td>
                                <td class="py-3 px-3 text-center text-white">🇩🇪🇷🇴🇳🇱🇮🇹</td>
                                <td class="py-3 px-3 text-center text-white">🇩🇪🇷🇴🇳🇱🇮🇹</td>
                                <td class="py-3 px-3 text-center text-white">🇩🇪🇷🇴🇳🇱🇮🇹</td>
                            </tr>
                            <tr>
                                <td class="py-3 px-3 text-gray-300 font-medium">Deployment</td>
                                <td class="py-3 px-3 text-center text-green-400">1 Hour</td>
                                <td class="py-3 px-3 text-center text-green-400">1 Hour</td>
                                <td class="py-3 px-3 text-center text-green-400">1 Hour</td>
                                <td class="py-3 px-3 text-center text-green-400">1 Hour</td>
                                <td class="py-3 px-3 text-center text-green-400">1 Hour</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- AMD EPYC Comparison -->
                <div class="bg-slate-900/30 rounded-3xl p-8 border border-slate-700/50 overflow-x-auto">
                    <h4 class="text-xl font-bold text-white mb-6 text-center">AMD EPYC AI-Optimized Server Comparison</h4>
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="border-b-2 border-slate-700">
                                <th class="text-left py-4 px-3 text-gray-300 font-semibold">Specifications</th>
                                <th class="text-center py-4 px-3 text-emerald-400 font-bold text-xs">Single 9254<br><span class="text-gray-400 font-normal">€449/mo</span></th>
                                <th class="text-center py-4 px-3 text-cyan-400 font-bold text-xs">Dual 9254<br><span class="text-gray-400 font-normal">€679/mo</span></th>
                                <th class="text-center py-4 px-3 text-violet-400 font-bold text-xs">Dual 9554<br><span class="text-gray-400 font-normal">€959/mo</span></th>
                                <th class="text-center py-4 px-3 text-pink-400 font-bold text-xs relative">
                                    Dual 9754<br><span class="text-gray-400 font-normal">€1519/mo</span>
                                    <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
                                        <span class="bg-pink-600 text-white px-2 py-1 rounded text-xs font-bold">ULTIMATE</span>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-3 text-gray-300 font-medium">Cores / Threads</td>
                                <td class="py-3 px-3 text-center text-emerald-400 font-bold">24c / 48t</td>
                                <td class="py-3 px-3 text-center text-cyan-400 font-bold">48c / 96t</td>
                                <td class="py-3 px-3 text-center text-violet-400 font-bold">128c / 256t</td>
                                <td class="py-3 px-3 text-center text-pink-400 font-bold">256c / 512t</td>
                            </tr>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-3 text-gray-300 font-medium">Clock Speed</td>
                                <td class="py-3 px-3 text-center text-white">2.9 GHz</td>
                                <td class="py-3 px-3 text-center text-white">2.9 GHz</td>
                                <td class="py-3 px-3 text-center text-white">3.1 GHz</td>
                                <td class="py-3 px-3 text-center text-white">2.3 GHz</td>
                            </tr>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-3 text-gray-300 font-medium">DDR5 RAM</td>
                                <td class="py-3 px-3 text-center text-white">128 GB</td>
                                <td class="py-3 px-3 text-center text-white">256 GB</td>
                                <td class="py-3 px-3 text-center text-white">512 GB</td>
                                <td class="py-3 px-3 text-center text-pink-400 font-bold">1024 GB</td>
                            </tr>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-3 text-gray-300 font-medium">NVMe Slots</td>
                                <td class="py-3 px-3 text-center text-white">10 slots</td>
                                <td class="py-3 px-3 text-center text-white">10 slots</td>
                                <td class="py-3 px-3 text-center text-white">24 slots</td>
                                <td class="py-3 px-3 text-center text-pink-400 font-bold">24 slots</td>
                            </tr>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-3 text-gray-300 font-medium">Bandwidth</td>
                                <td class="py-3 px-3 text-center text-white">1 Gbps</td>
                                <td class="py-3 px-3 text-center text-white">1 Gbps</td>
                                <td class="py-3 px-3 text-center text-white">1 Gbps</td>
                                <td class="py-3 px-3 text-center text-white">1 Gbps</td>
                            </tr>
                            <tr>
                                <td class="py-3 px-3 text-gray-300 font-medium">AI Optimization</td>
                                <td class="py-3 px-3 text-center text-emerald-400">✓ Included</td>
                                <td class="py-3 px-3 text-center text-cyan-400">✓ Included</td>
                                <td class="py-3 px-3 text-center text-violet-400">✓ Included</td>
                                <td class="py-3 px-3 text-center text-pink-400">✓ Included</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <!-- Enterprise Features -->
        <section class="py-24 bg-slate-950/50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">Enterprise-Grade Infrastructure</h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        Built for mission-critical applications with enterprise features and 24/7 expert support.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div class="text-center group">
                        <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-sky-600 rounded-2xl flex items-center justify-center mx-auto mb-6 transition-transform group-hover:scale-110">
                            <i data-lucide="shield-check" class="w-10 h-10 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">Advanced Security</h3>
                        <p class="text-gray-400 text-sm leading-relaxed">
                            Hardware firewalls, DDoS protection, and secure remote management with full root access control.
                        </p>
                    </div>
                    
                    <div class="text-center group">
                        <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6 transition-transform group-hover:scale-110">
                            <i data-lucide="activity" class="w-10 h-10 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">Real-time Monitoring</h3>
                        <p class="text-gray-400 text-sm leading-relaxed">
                            24/7 infrastructure monitoring with instant alerts and proactive maintenance notifications.
                        </p>
                    </div>
                    
                    <div class="text-center group">
                        <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6 transition-transform group-hover:scale-110">
                            <i data-lucide="headphones" class="w-10 h-10 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">Expert Support</h3>
                        <p class="text-gray-400 text-sm leading-relaxed">
                            Dedicated support team with streaming expertise available 24/7 via multiple channels.
                        </p>
                    </div>
                    
                    <div class="text-center group">
                        <div class="w-20 h-20 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-6 transition-transform group-hover:scale-110">
                            <i data-lucide="zap" class="w-10 h-10 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-3">Instant Provisioning</h3>
                        <p class="text-gray-400 text-sm leading-relaxed">
                            Automated deployment system gets your server online in under 1 hour with your custom configuration.
                        </p>
                    </div>
                </div>

                <!-- SLA Guarantee -->
                <div class="mt-16 bg-gradient-to-r from-slate-800/30 to-slate-700/50 rounded-3xl p-12 border border-slate-600/50 text-center">
                    <div class="max-w-4xl mx-auto">
                        <h3 class="text-3xl font-bold text-white mb-6">99.9% Uptime SLA Guarantee</h3>
                        <p class="text-xl text-gray-300 mb-8">
                            We guarantee 99.9% uptime with full SLA coverage. If we don't meet our commitment, you receive service credits automatically.
                        </p>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <div>
                                <div class="text-3xl font-bold text-sky-400 mb-2">99.9%</div>
                                <div class="text-gray-300">Uptime SLA</div>
                            </div>
                            <div>
                                <div class="text-3xl font-bold text-green-400 mb-2">< 1hr</div>
                                <div class="text-gray-300">Response Time</div>
                            </div>
                            <div>
                                <div class="text-3xl font-bold text-purple-400 mb-2">24/7</div>
                                <div class="text-gray-300">Expert Support</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-24 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">Ready to Get Started?</h2>
                <p class="text-xl text-gray-300 mb-12 max-w-3xl mx-auto">
                    Configure your dedicated streaming server and get online in under 1 hour. Our team is ready to help you scale your streaming infrastructure.
                </p>
                <div class="flex flex-col sm:flex-row gap-6 justify-center">
                    <a href="#server-configurator" class="btn-primary px-12 py-4 rounded-xl font-bold text-xl inline-flex items-center justify-center">
                        <i data-lucide="play-circle" class="w-6 h-6 mr-3"></i>
                        Configure Server Now
                    </a>
                    <a href="#" class="btn-secondary px-12 py-4 rounded-xl font-bold text-xl inline-flex items-center justify-center">
                        <i data-lucide="phone-call" class="w-6 h-6 mr-3"></i>
                        Talk to Expert
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-slate-900 border-t border-slate-800">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-8">
                <div class="col-span-2 lg:col-span-1">
                     <a href="index.html" class="text-2xl font-bold text-white flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-sky-400"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg>
                        X-Zone<span class="text-sky-400">Servers</span>
                    </a>
                    <p class="text-gray-400 mt-4 text-sm">Premium dedicated streaming servers worldwide.</p>
                </div>
                <div>
                    <h4 class="font-semibold text-white">Services</h4>
                    <ul class="mt-4 space-y-2 text-gray-400">
                        <li><a href="vps.html" class="hover:text-sky-400">VPS Hosting</a></li>
                        <li><a href="index.html#streaming-vps" class="hover:text-sky-400">Streaming VPS</a></li>
                        <li><a href="dedicated.html" class="hover:text-sky-400">Dedicated Servers</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold text-white">Company</h4>
                    <ul class="mt-4 space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-sky-400">About Us</a></li>
                        <li><a href="#" class="hover:text-sky-400">Contact</a></li>
                        <li><a href="#" class="hover:text-sky-400">Terms of Service</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold text-white">Support</h4>
                    <ul class="mt-4 space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-sky-400">Knowledgebase</a></li>
                        <li><a href="#" class="hover:text-sky-400">Open Ticket</a></li>
                        <li><a href="#" class="hover:text-sky-400">System Status</a></li>
                    </ul>
                </div>
            </div>
            <div class="mt-8 pt-8 border-t border-slate-800 text-center text-gray-500 text-sm">
                <p>&copy; 2024 X-ZoneServers SRL. All Rights Reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        lucide.createIcons();

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>