# X-ZoneServers Website

## Header Component Implementation

The website now uses a modular header component system for better maintainability.

### Files Structure

- `header.html` - Contains the shared header/navigation component
- `header-loader.js` - JavaScript to load the header into each page
- `index.html` - Main homepage (updated to use header component)
- `vps.html` - VPS plans page (updated to use header component)
- `dedicated.html` - Dedicated servers page (updated to use header component)

### How It Works

1. Each HTML page includes `header-loader.js` in the head section
2. Each page has a `<div id="header-placeholder"></div>` where the header will be loaded
3. The header-loader script fetches `header.html` and injects it into the placeholder
4. The header component includes JavaScript for:
   - Mobile menu toggle functionality
   - Active navigation highlighting based on current page
   - Dynamic CTA button text/links based on current page

### Benefits

- **Single Source of Truth**: Header content is maintained in one file
- **Consistent Navigation**: All pages automatically get the same navigation structure
- **Easy Updates**: Changes to the header only need to be made in `header.html`
- **Smart Active States**: Navigation automatically highlights the current page
- **Dynamic CTAs**: Call-to-action buttons adapt based on the current page

### Page-Specific Behavior

- **Index Page**: CTA button says "Get Started" and links to #streaming-vps
- **VPS Page**: CTA button says "Get Started" and links to #vps-plans
- **Dedicated Page**: CTA button says "Configure Server" and links to #server-configurator

### Navigation Links

The header includes navigation to:
- Streaming VPS (vps.html)
- Dedicated Streaming (dedicated.html)
- Features (index.html#features)
- FAQ (index.html#faq)

All navigation links are responsive and work on both desktop and mobile devices.
