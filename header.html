<!-- Header -->
<header class="fixed top-0 left-0 right-0 z-50 bg-slate-950/80 backdrop-blur-md">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-20">
            <a href="index.html" class="text-2xl font-bold text-white flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-sky-400"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg>
                X-Zone<span class="text-sky-400">Servers</span>
            </a>
            <nav class="hidden md:flex space-x-8">
                <a href="vps.html" class="text-gray-300 hover:text-sky-400 transition nav-link" data-page="vps">Streaming VPS</a>
                <a href="dedicated.html" class="text-gray-300 hover:text-sky-400 transition nav-link" data-page="dedicated">Dedicated Streaming</a>
                <a href="index.html#features" class="text-gray-300 hover:text-sky-400 transition nav-link" data-page="features">Features</a>
                <a href="index.html#faq" class="text-gray-300 hover:text-sky-400 transition nav-link" data-page="faq">FAQ</a>
            </nav>
            <a href="#streaming-vps" class="btn-secondary hidden md:inline-block px-6 py-2 rounded-lg font-semibold" id="header-cta">Get Started</a>
            <button id="mobile-menu-button" class="md:hidden text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg>
            </button>
        </div>
    </div>
    <!-- Mobile Menu -->
    <div id="mobile-menu" class="hidden md:hidden px-4 pb-4 space-y-2">
        <a href="vps.html" class="block text-gray-300 hover:text-sky-400 transition py-2 mobile-nav-link" data-page="vps">Streaming VPS</a>
        <a href="dedicated.html" class="block text-gray-300 hover:text-sky-400 transition py-2 mobile-nav-link" data-page="dedicated">Dedicated Streaming</a>
        <a href="index.html#features" class="block text-gray-300 hover:text-sky-400 transition py-2 mobile-nav-link" data-page="features">Features</a>
        <a href="index.html#faq" class="block text-gray-300 hover:text-sky-400 transition py-2 mobile-nav-link" data-page="faq">FAQ</a>
        <a href="#streaming-vps" class="btn-secondary w-full text-center mt-4 px-6 py-2 rounded-lg font-semibold" id="mobile-header-cta">Get Started</a>
    </div>
</header>

<script>
// Header navigation functionality
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', () => {
            mobileMenu.classList.toggle('hidden');
        });
    }

    // Set active navigation based on current page
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '') || 'index';
    const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');
    
    navLinks.forEach(link => {
        const linkPage = link.getAttribute('data-page');
        if (currentPage === 'index' && (linkPage === 'features' || linkPage === 'faq')) {
            // Home page sections
            link.classList.remove('text-gray-300');
            link.classList.add('text-sky-400', 'font-semibold');
        } else if (currentPage === linkPage) {
            // Specific pages
            link.classList.remove('text-gray-300');
            link.classList.add('text-sky-400', 'font-semibold');
        }
    });

    // Update CTA button based on current page
    const headerCta = document.getElementById('header-cta');
    const mobileHeaderCta = document.getElementById('mobile-header-cta');
    
    if (currentPage === 'vps') {
        if (headerCta) {
            headerCta.textContent = 'Get Started';
            headerCta.href = '#vps-plans';
        }
        if (mobileHeaderCta) {
            mobileHeaderCta.textContent = 'Get Started';
            mobileHeaderCta.href = '#vps-plans';
        }
    } else if (currentPage === 'dedicated') {
        if (headerCta) {
            headerCta.textContent = 'Configure Server';
            headerCta.href = '#server-configurator';
        }
        if (mobileHeaderCta) {
            mobileHeaderCta.textContent = 'Configure Server';
            mobileHeaderCta.href = '#server-configurator';
        }
    } else {
        // Default for index page
        if (headerCta) {
            headerCta.textContent = 'Get Started';
            headerCta.href = '#streaming-vps';
        }
        if (mobileHeaderCta) {
            mobileHeaderCta.textContent = 'Get Started';
            mobileHeaderCta.href = '#streaming-vps';
        }
    }
});
</script>
